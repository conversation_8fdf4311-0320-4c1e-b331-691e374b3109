#!/usr/bin/env python3
"""
Targeted Polygon-Based Distance Evaluation for Specific 7 GNN Models

This system evaluates the exact 7 models specified in the research table:
- Complex GATv2 T3 & T5
- Standard GATv2 T3 & T5  
- ECC T3 & T5
- Enhanced GATv2 T3

Author: AI Assistant
Date: 2024
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from tqdm import tqdm
import yaml
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import the working comprehensive evaluation system
from comprehensive_2d_iou_evaluation import Comprehensive2DIoUEvaluator, ModelInfo, IoUResults
from data import OccupancyDataset
from torch_geometric.loader import DataLoader

# Geometric computation libraries
from shapely.geometry import Point, Polygon, LineString
from shapely.ops import unary_union
from scipy.spatial.distance import cdist
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score


@dataclass
class TargetedModelInfo:
    """Information about the specific 7 target models."""
    name: str
    architecture: str
    temporal_window: int
    checkpoint_path: str
    config_path: Optional[str]
    parameters: int
    epochs: int
    training_time: str
    model_size: str
    convergence: str
    description: str


@dataclass
class PolygonDistanceResults:
    """Results from polygon-based distance evaluation."""
    model_name: str
    architecture: str
    temporal_window: int
    parameters: int
    
    # Traditional metrics
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    roc_auc: float
    
    # Distance-based metrics
    mean_distance_error: float
    median_distance_error: float
    std_distance_error: float
    percentile_90_distance: float
    percentile_95_distance: float
    percentile_99_distance: float
    
    # Tolerance-based accuracy
    within_15cm_accuracy: float
    within_20cm_accuracy: float
    within_25cm_accuracy: float
    
    # Sample information
    total_samples: int
    total_predictions: int
    occupied_predictions: int
    
    # Performance metrics
    training_time: str
    model_size: str
    convergence: str


class TargetedPolygonDistanceEvaluator(Comprehensive2DIoUEvaluator):
    """
    Targeted evaluator for the specific 7 models in the research table.
    """
    
    def __init__(self, results_dir: str = "targeted_polygon_distance_evaluation"):
        """Initialize the targeted polygon distance evaluator."""
        # Initialize parent with correct data directory
        super().__init__(
            gnn_occupancy_dir=".",
            data_dir="/home/<USER>/ma_yugi/data/07_gnn_ready",
            results_dir=results_dir
        )
        
        # Arena specifications for polygon generation
        self.arena_bounds = (-10.39, 10.67, -5.31, 6.50)  # (min_x, max_x, min_y, max_y)
        self.voxel_size = 0.1  # meters
        
        # Object dimensions
        self.workstation_width = 1.0  # meters
        self.workstation_height = 0.65  # meters
        self.robot_width = 0.32  # meters
        self.robot_height = 0.24  # meters
        
        # Fixed workstation positions (simplified)
        self.workstation_positions = [
            (0.0, 0.0, 0.0),    # (x, y, rotation_deg)
            (2.0, 0.0, 0.0),
            (4.0, 0.0, 0.0),
            (6.0, 0.0, 90.0),   # Vertical
            (8.0, 0.0, 0.0),
        ]
        
        # Define the specific 7 target models
        self.target_models = self._define_target_models()
        
        # Storage for results
        self.polygon_results: Dict[str, PolygonDistanceResults] = {}
    
    def _define_target_models(self) -> List[TargetedModelInfo]:
        """Define the specific 7 models from the research table."""
        models = [
            TargetedModelInfo(
                name="Complex_GATv2_T3",
                architecture="Complex GATv2",
                temporal_window=3,
                checkpoint_path="models_final/checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt",
                config_path="models_final/checkpoints_gatv2_complex_4layers_temp3/config.yaml",
                parameters=169601,
                epochs=86,
                training_time="4.0h",
                model_size="45MB",
                convergence="Stable",
                description="Complex GATv2 with 4 layers, temporal window 3"
            ),
            TargetedModelInfo(
                name="Complex_GATv2_T5",
                architecture="Complex GATv2",
                temporal_window=5,
                checkpoint_path="models_final/checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt",
                config_path=None,  # No config file found
                parameters=169601,
                epochs=59,
                training_time="2.5h",
                model_size="45MB",
                convergence="Stable",
                description="Complex GATv2 with temporal window 5"
            ),
            TargetedModelInfo(
                name="Standard_GATv2_T3",
                architecture="Standard GATv2",
                temporal_window=3,
                checkpoint_path="models_final/checkpoints_gatv2_temp3/model_temporal_3_best.pt",
                config_path="models_final/checkpoints_gatv2_temp3/config.yaml",
                parameters=25793,
                epochs=22,
                training_time="1.0h",
                model_size="12MB",
                convergence="Rapid",
                description="Standard GATv2 with temporal window 3"
            ),
            TargetedModelInfo(
                name="Standard_GATv2_T5",
                architecture="Standard GATv2",
                temporal_window=5,
                checkpoint_path="models_final/checkpoints_temp5/model_temporal_5_best.pt",
                config_path="models_final/checkpoints_temp5/config.yaml",
                parameters=30273,
                epochs=32,
                training_time="1.5h",
                model_size="15MB",
                convergence="Rapid",
                description="Standard GATv2 with temporal window 5"
            ),
            TargetedModelInfo(
                name="ECC_T3",
                architecture="ECC",
                temporal_window=3,
                checkpoint_path="models_final/checkpoints_ecc_temp3/model_temporal_3_best.pt",
                config_path=None,  # ECC models don't have config files
                parameters=50390401,
                epochs=23,
                training_time="30min",
                model_size="180MB",
                convergence="Fast",
                description="ECC model with temporal window 3"
            ),
            TargetedModelInfo(
                name="ECC_T5",
                architecture="ECC",
                temporal_window=5,
                checkpoint_path="models_final/checkpoints_ecc_temp5/model_temporal_5_best.pt",
                config_path=None,  # ECC models don't have config files
                parameters=2106977,
                epochs=84,
                training_time="21min",
                model_size="55MB",
                convergence="Extended",
                description="ECC model with temporal window 5"
            ),
            TargetedModelInfo(
                name="Enhanced_GATv2_T3",
                architecture="Enhanced GATv2",
                temporal_window=3,
                checkpoint_path="models_final/checkpoints_enhanced_temp3/model_temporal_3_best.pt",
                config_path=None,  # No config file found
                parameters=6045313,
                epochs=11,  # Early stopped at 11/36
                training_time="6.0h",
                model_size="220MB",
                convergence="Early Stop",
                description="Enhanced GATv2 with temporal window 3 (early stopped)"
            )
        ]
        
        # Verify that all checkpoint files exist
        verified_models = []
        for model in models:
            checkpoint_path = Path(model.checkpoint_path)
            if checkpoint_path.exists():
                verified_models.append(model)
                print(f"✅ Found: {model.name} at {checkpoint_path}")
            else:
                print(f"❌ Missing: {model.name} at {checkpoint_path}")
        
        return verified_models

    def create_workstation_polygons(self) -> List[Polygon]:
        """Create polygons for all workstations."""
        polygons = []

        for x, y, rotation_deg in self.workstation_positions:
            polygon = self._create_rotated_rectangle(
                x, y, self.workstation_width, self.workstation_height, rotation_deg
            )
            polygons.append(polygon)

        return polygons

    def _create_rotated_rectangle(self, center_x: float, center_y: float,
                                width: float, height: float, rotation_deg: float) -> Polygon:
        """Create a rotated rectangle polygon."""
        # Create rectangle corners relative to center
        half_w, half_h = width / 2, height / 2
        corners = np.array([
            [-half_w, -half_h],
            [half_w, -half_h],
            [half_w, half_h],
            [-half_w, half_h]
        ])

        # Apply rotation
        rotation_rad = np.radians(rotation_deg)
        cos_r, sin_r = np.cos(rotation_rad), np.sin(rotation_rad)
        rotation_matrix = np.array([[cos_r, -sin_r], [sin_r, cos_r]])

        rotated_corners = corners @ rotation_matrix.T

        # Translate to center position
        final_corners = rotated_corners + np.array([center_x, center_y])

        return Polygon(final_corners)

    def point_to_polygon_distance(self, points: np.ndarray, polygons: List[Polygon]) -> np.ndarray:
        """Calculate minimum distance from points to polygon boundaries."""
        if not polygons or len(points) == 0:
            return np.array([])

        min_distances = np.full(len(points), np.inf)

        for polygon in polygons:
            boundary = polygon.boundary

            for i, point in enumerate(points):
                point_geom = Point(point)
                distance = point_geom.distance(boundary)
                min_distances[i] = min(min_distances[i], distance)

        return min_distances

    def calculate_tolerance_accuracy(self, distances: np.ndarray, tolerance: float) -> float:
        """Calculate accuracy within a distance tolerance."""
        if len(distances) == 0:
            return 0.0
        return np.mean(distances <= tolerance)

    def _load_model_with_inference(self, config, checkpoint):
        """Load model with parameter inference from checkpoint."""
        from model import create_model

        try:
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            return model
        except RuntimeError as e:
            if "size mismatch" in str(e):
                print(f"  ⚠️ Model architecture mismatch detected")
                print(f"  🔧 Attempting to infer correct architecture from checkpoint...")

                # Infer input dimension from checkpoint
                if 'embedding.weight' in checkpoint['model_state_dict']:
                    embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                    if len(embedding_shape) >= 2:
                        inferred_input_dim = embedding_shape[1]
                        print(f"  📊 Inferred input dimension: {inferred_input_dim}")
                        config['model']['input_dim'] = inferred_input_dim

                # Infer attention heads for GATv2 models
                if 'convs.0.att' in checkpoint['model_state_dict']:
                    att_shape = checkpoint['model_state_dict']['convs.0.att'].shape
                    if len(att_shape) >= 2:
                        inferred_heads = att_shape[1]
                        print(f"  📊 Inferred attention heads: {inferred_heads}")
                        config['model']['attention_heads'] = inferred_heads

                # Try creating model again with inferred parameters
                try:
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"  ✅ Successfully loaded model with corrected architecture")
                    return model
                except RuntimeError:
                    # If we still can't load, try strict=False as last resort
                    print(f"  ⚠️ Still cannot load, trying strict=False")
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'], strict=False)
                    return model
            else:
                raise e

    def _create_config_for_model(self, model_info: TargetedModelInfo) -> Dict:
        """Create configuration for a specific model."""
        if model_info.config_path and Path(model_info.config_path).exists():
            with open(model_info.config_path, 'r') as f:
                config = yaml.safe_load(f)
        elif "ECC" in model_info.architecture:
            # Create ECC config
            config = {
                "data": {
                    "data_dir": "/home/<USER>/ma_yugi/data/07_gnn_ready/",
                    "batch_size": 32,
                    "num_workers": 4,
                    "temporal_windows": [model_info.temporal_window],
                    "binary_mapping": {
                        "occupied": [1, 2, 3, 4],
                        "unoccupied": [0]
                    },
                    "augmentation": {
                        "rotation_angle": 15,
                        "scaling_range": [0.9, 1.1]
                    }
                },
                "model": {
                    "name": "OccupancyGNN",
                    "input_dim": 10 if model_info.temporal_window > 1 else 9,
                    "hidden_dim": 64,
                    "output_dim": 1,
                    "num_layers": 3,
                    "dropout": 0.2,
                    "gnn_type": "ecc",
                    "skip_connections": True,
                    "batch_norm": True,
                    "pooling": "mean_max"
                }
            }
        else:
            # Create default config for other models
            config = {
                "data": {
                    "data_dir": "/home/<USER>/ma_yugi/data/07_gnn_ready/",
                    "batch_size": 32,
                    "num_workers": 4,
                    "temporal_windows": [model_info.temporal_window],
                    "binary_mapping": {
                        "occupied": [1, 2, 3, 4],
                        "unoccupied": [0]
                    },
                    "augmentation": {
                        "rotation_angle": 15,
                        "scaling_range": [0.9, 1.1]
                    }
                },
                "model": {
                    "name": "OccupancyGNN",
                    "input_dim": 10 if model_info.temporal_window > 1 else 9,
                    "hidden_dim": 128 if "Complex" in model_info.architecture else 64,
                    "output_dim": 1,
                    "num_layers": 4 if "Complex" in model_info.architecture else 3,
                    "dropout": 0.3 if "Complex" in model_info.architecture else 0.2,
                    "gnn_type": "gatv2",
                    "skip_connections": True,
                    "batch_norm": True,
                    "pooling": "mean_max",
                    "attention_heads": 8 if "Complex" in model_info.architecture else 4,
                    "layer_norm": "Complex" in model_info.architecture
                }
            }

        return config

    def evaluate_model_polygon_distance(self, model_info: TargetedModelInfo, max_samples: int = 1000) -> PolygonDistanceResults:
        """
        Evaluate a single model using polygon-based distance metrics.
        """
        print(f"🔍 Evaluating polygon distance for model: {model_info.name}")
        print(f"   Architecture: {model_info.architecture}")
        print(f"   Parameters: {model_info.parameters:,}")
        print(f"   Training: {model_info.epochs} epochs, {model_info.training_time}")

        # Load model configuration
        config = self._create_config_for_model(model_info)

        # Load model checkpoint
        checkpoint = torch.load(model_info.checkpoint_path, map_location=self.device)

        try:
            model = self._load_model_with_inference(config, checkpoint)
        except Exception as e:
            print(f"❌ Failed to load model for polygon evaluation: {e}")
            return PolygonDistanceResults(
                model_name=model_info.name,
                architecture=model_info.architecture,
                temporal_window=model_info.temporal_window,
                parameters=model_info.parameters,
                accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0, roc_auc=0.0,
                mean_distance_error=0.0, median_distance_error=0.0, std_distance_error=0.0,
                percentile_90_distance=0.0, percentile_95_distance=0.0, percentile_99_distance=0.0,
                within_15cm_accuracy=0.0, within_20cm_accuracy=0.0, within_25cm_accuracy=0.0,
                total_samples=0, total_predictions=0, occupied_predictions=0,
                training_time=model_info.training_time, model_size=model_info.model_size,
                convergence=model_info.convergence
            )

        model.to(self.device)
        model.eval()

        # Load test data
        test_loader = self.load_test_data(model_info.temporal_window)

        # Check if test loader has data
        print(f"  📊 Test loader has {len(test_loader)} batches")
        if len(test_loader) == 0:
            print(f"  ⚠️ No test data found for temporal window {model_info.temporal_window}")
            return PolygonDistanceResults(
                model_name=model_info.name,
                architecture=model_info.architecture,
                temporal_window=model_info.temporal_window,
                parameters=model_info.parameters,
                accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0, roc_auc=0.0,
                mean_distance_error=0.0, median_distance_error=0.0, std_distance_error=0.0,
                percentile_90_distance=0.0, percentile_95_distance=0.0, percentile_99_distance=0.0,
                within_15cm_accuracy=0.0, within_20cm_accuracy=0.0, within_25cm_accuracy=0.0,
                total_samples=0, total_predictions=0, occupied_predictions=0,
                training_time=model_info.training_time, model_size=model_info.model_size,
                convergence=model_info.convergence
            )

        # Create ground truth polygons
        workstation_polygons = self.create_workstation_polygons()

        # Collect predictions and calculate distances
        all_distances = []
        all_predictions = []
        all_ground_truth = []
        all_probabilities = []

        samples_processed = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Processing samples")):
                if samples_processed >= max_samples:
                    break

                try:
                    batch = batch.to(self.device)

                    # Forward pass
                    logits = model(batch.x, batch.edge_index, batch.batch)
                    probabilities = torch.sigmoid(logits).cpu().numpy()
                    predictions = (probabilities > 0.5).astype(int)
                    ground_truth = batch.y.cpu().numpy()

                    # Handle different prediction formats
                    if len(predictions) == 1 and len(ground_truth) > 1:
                        # Graph-level prediction: broadcast to all nodes
                        predictions = np.full(len(ground_truth), predictions[0])
                    elif len(predictions) != len(ground_truth):
                        print(f"    ⚠️ Prediction-label size mismatch: {len(predictions)} vs {len(ground_truth)}")
                        continue

                    # Extract spatial coordinates from node features
                    try:
                        # First try pos attribute
                        coordinates = batch.pos[:, :2].cpu().numpy()  # x, y coordinates
                    except (AttributeError, IndexError):
                        try:
                            # Fallback to features 3, 4 (x, y coordinates based on our analysis)
                            coordinates = batch.x[:, 3:5].cpu().numpy()
                        except IndexError:
                            print(f"    ⚠️ Cannot extract coordinates from batch {batch_idx}")
                            continue

                    # Find occupied predictions
                    occupied_mask = predictions.flatten() == 1
                    if np.any(occupied_mask):
                        occupied_coords = coordinates[occupied_mask]

                        # Calculate distances to workstation polygons
                        distances = self.point_to_polygon_distance(occupied_coords, workstation_polygons)
                        all_distances.extend(distances)

                    # Store all predictions for traditional metrics
                    all_predictions.extend(predictions.flatten())
                    all_ground_truth.extend(ground_truth.flatten())
                    all_probabilities.extend(probabilities.flatten())

                    samples_processed += 1

                except Exception as e:
                    print(f"    ⚠️ Error processing batch {batch_idx}: {e}")
                    continue

        # Convert to numpy arrays
        all_predictions = np.array(all_predictions)
        all_ground_truth = np.array(all_ground_truth)
        all_probabilities = np.array(all_probabilities)
        all_distances = np.array(all_distances)

        # Calculate traditional metrics
        accuracy = accuracy_score(all_ground_truth, all_predictions)
        precision = precision_score(all_ground_truth, all_predictions, zero_division=0)
        recall = recall_score(all_ground_truth, all_predictions, zero_division=0)
        f1 = f1_score(all_ground_truth, all_predictions, zero_division=0)

        try:
            roc_auc = roc_auc_score(all_ground_truth, all_probabilities)
        except ValueError:
            roc_auc = 0.0

        # Calculate distance-based metrics
        if len(all_distances) > 0:
            mean_distance = float(np.mean(all_distances))
            median_distance = float(np.median(all_distances))
            std_distance = float(np.std(all_distances))
            p90_distance = float(np.percentile(all_distances, 90))
            p95_distance = float(np.percentile(all_distances, 95))
            p99_distance = float(np.percentile(all_distances, 99))

            # Tolerance-based accuracy
            within_15cm = self.calculate_tolerance_accuracy(all_distances, 0.15)
            within_20cm = self.calculate_tolerance_accuracy(all_distances, 0.20)
            within_25cm = self.calculate_tolerance_accuracy(all_distances, 0.25)
        else:
            mean_distance = median_distance = std_distance = 0.0
            p90_distance = p95_distance = p99_distance = 0.0
            within_15cm = within_20cm = within_25cm = 0.0

        # Create results object
        results = PolygonDistanceResults(
            model_name=model_info.name,
            architecture=model_info.architecture,
            temporal_window=model_info.temporal_window,
            parameters=model_info.parameters,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            roc_auc=roc_auc,
            mean_distance_error=mean_distance,
            median_distance_error=median_distance,
            std_distance_error=std_distance,
            percentile_90_distance=p90_distance,
            percentile_95_distance=p95_distance,
            percentile_99_distance=p99_distance,
            within_15cm_accuracy=within_15cm,
            within_20cm_accuracy=within_20cm,
            within_25cm_accuracy=within_25cm,
            total_samples=samples_processed,
            total_predictions=len(all_predictions),
            occupied_predictions=int(np.sum(all_predictions)),
            training_time=model_info.training_time,
            model_size=model_info.model_size,
            convergence=model_info.convergence
        )

        print(f"  ✅ Completed polygon distance evaluation")
        print(f"     Traditional Accuracy: {accuracy:.4f}")
        print(f"     Mean Distance Error: {mean_distance:.4f}m")
        print(f"     Within 20cm Accuracy: {within_20cm:.4f}")

        return results

    def evaluate_all_target_models(self, max_samples: int = 1000) -> Dict[str, PolygonDistanceResults]:
        """Evaluate all 7 target models using polygon-based distance metrics."""
        print("🚀 Starting targeted polygon-based distance evaluation...")
        print(f"📊 Target models: {len(self.target_models)}")

        results = {}

        for i, model_info in enumerate(self.target_models, 1):
            print(f"\n{'='*60}")
            print(f"EVALUATING MODEL {i}/{len(self.target_models)}: {model_info.name}")
            print(f"{'='*60}")

            try:
                polygon_results = self.evaluate_model_polygon_distance(model_info, max_samples)
                results[model_info.name] = polygon_results
                self.polygon_results[model_info.name] = polygon_results

                # Save intermediate results
                self._save_polygon_results(model_info.name, polygon_results)

            except Exception as e:
                print(f"❌ Failed to evaluate {model_info.name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        print(f"\n🎯 Completed targeted polygon distance evaluation of {len(results)} models")
        return results

    def _save_polygon_results(self, model_name: str, results: PolygonDistanceResults):
        """Save individual model polygon distance results."""
        results_dict = {
            'model_name': model_name,
            'architecture': results.architecture,
            'temporal_window': results.temporal_window,
            'parameters': results.parameters,
            'timestamp': datetime.now().isoformat(),
            'traditional_metrics': {
                'accuracy': results.accuracy,
                'precision': results.precision,
                'recall': results.recall,
                'f1_score': results.f1_score,
                'roc_auc': results.roc_auc
            },
            'distance_metrics': {
                'mean_distance_error': results.mean_distance_error,
                'median_distance_error': results.median_distance_error,
                'std_distance_error': results.std_distance_error,
                'percentile_90_distance': results.percentile_90_distance,
                'percentile_95_distance': results.percentile_95_distance,
                'percentile_99_distance': results.percentile_99_distance
            },
            'tolerance_metrics': {
                'within_15cm_accuracy': results.within_15cm_accuracy,
                'within_20cm_accuracy': results.within_20cm_accuracy,
                'within_25cm_accuracy': results.within_25cm_accuracy
            },
            'sample_info': {
                'total_samples': results.total_samples,
                'total_predictions': results.total_predictions,
                'occupied_predictions': results.occupied_predictions
            },
            'performance_info': {
                'training_time': results.training_time,
                'model_size': results.model_size,
                'convergence': results.convergence
            }
        }

        # Save to JSON
        output_file = self.results_dir / f"{model_name}_polygon_distance_results.json"
        with open(output_file, 'w') as f:
            json.dump(results_dict, f, indent=2)

    def create_targeted_visualizations(self, results: Dict[str, PolygonDistanceResults]):
        """Create comprehensive visualizations for the 7 target models."""
        print("📊 Creating targeted polygon distance comparison visualizations...")

        if not results:
            print("❌ No results to visualize!")
            return

        # Prepare data for visualization
        model_names = list(results.keys())

        # Create comprehensive comparison plot
        fig, axes = plt.subplots(3, 3, figsize=(24, 18))
        fig.suptitle('Targeted Polygon-Based Distance Evaluation: 7 Research Models Comparison',
                     fontsize=18, fontweight='bold')

        # 1. Traditional metrics comparison
        ax = axes[0, 0]
        traditional_data = {
            'Accuracy': [results[name].accuracy for name in model_names],
            'Precision': [results[name].precision for name in model_names],
            'Recall': [results[name].recall for name in model_names],
            'F1-Score': [results[name].f1_score for name in model_names]
        }
        df_traditional = pd.DataFrame(traditional_data, index=model_names)
        df_traditional.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Traditional Classification Metrics', fontweight='bold', fontsize=12)
        ax.set_ylabel('Score')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # 2. Distance metrics comparison
        ax = axes[0, 1]
        distance_data = {
            'Mean Distance (m)': [results[name].mean_distance_error for name in model_names],
            'Median Distance (m)': [results[name].median_distance_error for name in model_names],
            '90th Percentile (m)': [results[name].percentile_90_distance for name in model_names]
        }
        df_distance = pd.DataFrame(distance_data, index=model_names)
        df_distance.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Distance-Based Error Metrics', fontweight='bold', fontsize=12)
        ax.set_ylabel('Distance (meters)')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # 3. Tolerance metrics comparison
        ax = axes[0, 2]
        tolerance_data = {
            'Within 15cm': [results[name].within_15cm_accuracy for name in model_names],
            'Within 20cm': [results[name].within_20cm_accuracy for name in model_names],
            'Within 25cm': [results[name].within_25cm_accuracy for name in model_names]
        }
        df_tolerance = pd.DataFrame(tolerance_data, index=model_names)
        df_tolerance.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Tolerance-Based Accuracy', fontweight='bold', fontsize=12)
        ax.set_ylabel('Accuracy')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # 4. Mean distance error with error bars
        ax = axes[1, 0]
        mean_errors = [results[name].mean_distance_error for name in model_names]
        std_errors = [results[name].std_distance_error for name in model_names]

        bars = ax.bar(range(len(model_names)), mean_errors, yerr=std_errors, capsize=5)
        ax.set_title('Mean Distance Error with Standard Deviation', fontweight='bold', fontsize=12)
        ax.set_ylabel('Distance Error (meters)')
        ax.set_xticks(range(len(model_names)))
        ax.set_xticklabels(model_names, rotation=45, ha='right')

        # Add value labels on bars
        for i, (bar, mean_err) in enumerate(zip(bars, mean_errors)):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std_errors[i] + 0.001,
                   f'{mean_err:.3f}m', ha='center', va='bottom', fontsize=8)

        # 5. Architecture comparison
        ax = axes[1, 1]
        architectures = {}
        for name in model_names:
            arch = results[name].architecture
            if arch not in architectures:
                architectures[arch] = []
            architectures[arch].append(results[name].mean_distance_error)

        arch_names = list(architectures.keys())
        arch_means = [np.mean(architectures[arch]) for arch in arch_names]
        arch_stds = [np.std(architectures[arch]) if len(architectures[arch]) > 1 else 0 for arch in arch_names]

        bars = ax.bar(arch_names, arch_means, yerr=arch_stds, capsize=5)
        ax.set_title('Architecture Comparison (Mean Distance Error)', fontweight='bold', fontsize=12)
        ax.set_ylabel('Mean Distance Error (meters)')
        ax.tick_params(axis='x', rotation=45)

        # 6. Temporal window comparison
        ax = axes[1, 2]
        temporal_windows = {}
        for name in model_names:
            tw = f"T{results[name].temporal_window}"
            if tw not in temporal_windows:
                temporal_windows[tw] = []
            temporal_windows[tw].append(results[name].mean_distance_error)

        tw_names = list(temporal_windows.keys())
        tw_means = [np.mean(temporal_windows[tw]) for tw in tw_names]
        tw_stds = [np.std(temporal_windows[tw]) if len(temporal_windows[tw]) > 1 else 0 for tw in tw_names]

        bars = ax.bar(tw_names, tw_means, yerr=tw_stds, capsize=5)
        ax.set_title('Temporal Window Comparison (Mean Distance Error)', fontweight='bold', fontsize=12)
        ax.set_ylabel('Mean Distance Error (meters)')

        # 7. Model parameters vs performance
        ax = axes[2, 0]
        parameters = [results[name].parameters for name in model_names]
        distance_errors = [results[name].mean_distance_error for name in model_names]

        scatter = ax.scatter(parameters, distance_errors, s=100, alpha=0.7)
        ax.set_title('Model Parameters vs Distance Error', fontweight='bold', fontsize=12)
        ax.set_xlabel('Number of Parameters')
        ax.set_ylabel('Mean Distance Error (meters)')
        ax.set_xscale('log')

        # Add model name labels
        for i, name in enumerate(model_names):
            ax.annotate(name.replace('_', '\n'), (parameters[i], distance_errors[i]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)

        # 8. Training efficiency analysis
        ax = axes[2, 1]
        # Convert training time to hours for comparison
        training_hours = []
        for name in model_names:
            time_str = results[name].training_time
            if 'h' in time_str:
                hours = float(time_str.replace('h', ''))
            elif 'min' in time_str:
                hours = float(time_str.replace('min', '')) / 60
            else:
                hours = 1.0  # Default
            training_hours.append(hours)

        accuracy_scores = [results[name].accuracy for name in model_names]

        scatter = ax.scatter(training_hours, accuracy_scores, s=100, alpha=0.7)
        ax.set_title('Training Time vs Accuracy', fontweight='bold', fontsize=12)
        ax.set_xlabel('Training Time (hours)')
        ax.set_ylabel('Accuracy')

        # Add model name labels
        for i, name in enumerate(model_names):
            ax.annotate(name.replace('_', '\n'), (training_hours[i], accuracy_scores[i]),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)

        # 9. Convergence analysis
        ax = axes[2, 2]
        convergence_types = {}
        for name in model_names:
            conv = results[name].convergence
            if conv not in convergence_types:
                convergence_types[conv] = []
            convergence_types[conv].append(results[name].mean_distance_error)

        conv_names = list(convergence_types.keys())
        conv_means = [np.mean(convergence_types[conv]) for conv in conv_names]
        conv_stds = [np.std(convergence_types[conv]) if len(convergence_types[conv]) > 1 else 0 for conv in conv_names]

        bars = ax.bar(conv_names, conv_means, yerr=conv_stds, capsize=5)
        ax.set_title('Convergence Type vs Distance Error', fontweight='bold', fontsize=12)
        ax.set_ylabel('Mean Distance Error (meters)')
        ax.tick_params(axis='x', rotation=45)

        plt.tight_layout()

        # Save the plot
        output_file = self.results_dir / "targeted_polygon_distance_comprehensive_comparison.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.savefig(output_file.with_suffix('.pdf'), bbox_inches='tight')
        print(f"💾 Saved comparison plot: {output_file}")

        plt.show()

        return fig

    def generate_targeted_report(self, results: Dict[str, PolygonDistanceResults]):
        """Generate a comprehensive report for the 7 target models."""
        print("📝 Generating targeted polygon distance evaluation report...")

        # Create summary DataFrame
        summary_data = []
        for model_name, metrics in results.items():
            summary_data.append({
                'Model': model_name,
                'Architecture': metrics.architecture,
                'Temporal_Window': f"T{metrics.temporal_window}",
                'Parameters': metrics.parameters,
                'Training_Time': metrics.training_time,
                'Model_Size': metrics.model_size,
                'Convergence': metrics.convergence,
                'Accuracy': metrics.accuracy,
                'Precision': metrics.precision,
                'Recall': metrics.recall,
                'F1_Score': metrics.f1_score,
                'ROC_AUC': metrics.roc_auc,
                'Mean_Distance_Error': metrics.mean_distance_error,
                'Median_Distance_Error': metrics.median_distance_error,
                'Std_Distance_Error': metrics.std_distance_error,
                'P90_Distance': metrics.percentile_90_distance,
                'P95_Distance': metrics.percentile_95_distance,
                'P99_Distance': metrics.percentile_99_distance,
                'Within_15cm': metrics.within_15cm_accuracy,
                'Within_20cm': metrics.within_20cm_accuracy,
                'Within_25cm': metrics.within_25cm_accuracy,
                'Total_Samples': metrics.total_samples,
                'Total_Predictions': metrics.total_predictions,
                'Occupied_Predictions': metrics.occupied_predictions
            })

        df_summary = pd.DataFrame(summary_data)

        # Save detailed CSV
        csv_file = self.results_dir / "targeted_polygon_distance_evaluation_results.csv"
        df_summary.to_csv(csv_file, index=False)
        print(f"💾 Saved detailed results: {csv_file}")

        # Generate markdown report
        report_file = self.results_dir / "TARGETED_POLYGON_DISTANCE_EVALUATION_REPORT.md"

        with open(report_file, 'w') as f:
            f.write("# Targeted Polygon-Based Distance Evaluation Report\n\n")
            f.write("## Research Models Evaluation\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Models Evaluated:** {len(results)} (7 target research models)\n\n")

            f.write("## Executive Summary\n\n")

            # Find best performing models
            best_accuracy = df_summary.loc[df_summary['Accuracy'].idxmax()]
            best_distance = df_summary.loc[df_summary['Mean_Distance_Error'].idxmin()]
            best_tolerance = df_summary.loc[df_summary['Within_20cm'].idxmax()]

            f.write(f"- **Best Traditional Accuracy:** {best_accuracy['Model']} ({best_accuracy['Accuracy']:.4f})\n")
            f.write(f"- **Best Distance Accuracy:** {best_distance['Model']} ({best_distance['Mean_Distance_Error']:.4f}m)\n")
            f.write(f"- **Best 20cm Tolerance:** {best_tolerance['Model']} ({best_tolerance['Within_20cm']:.4f})\n\n")

            f.write("## Model Specifications\n\n")
            f.write("| Model | Architecture | Temporal Window | Parameters | Training Time | Model Size | Convergence |\n")
            f.write("|-------|-------------|----------------|------------|---------------|------------|-------------|\n")
            for _, row in df_summary.iterrows():
                f.write(f"| {row['Model']} | {row['Architecture']} | {row['Temporal_Window']} | {row['Parameters']:,} | {row['Training_Time']} | {row['Model_Size']} | {row['Convergence']} |\n")
            f.write("\n")

            f.write("## Performance Results\n\n")
            f.write("### Traditional Classification Metrics\n\n")
            f.write(df_summary[['Model', 'Accuracy', 'Precision', 'Recall', 'F1_Score', 'ROC_AUC']].to_markdown(index=False))
            f.write("\n\n")

            f.write("### Distance-Based Metrics\n\n")
            f.write(df_summary[['Model', 'Mean_Distance_Error', 'Median_Distance_Error', 'P90_Distance', 'P95_Distance']].to_markdown(index=False))
            f.write("\n\n")

            f.write("### Tolerance-Based Accuracy\n\n")
            f.write(df_summary[['Model', 'Within_15cm', 'Within_20cm', 'Within_25cm']].to_markdown(index=False))
            f.write("\n\n")

            f.write("## Architecture Analysis\n\n")
            arch_analysis = df_summary.groupby('Architecture').agg({
                'Mean_Distance_Error': ['mean', 'std', 'min', 'max'],
                'Accuracy': ['mean', 'std', 'min', 'max'],
                'Within_20cm': ['mean', 'std', 'min', 'max'],
                'Parameters': ['mean', 'min', 'max']
            }).round(4)
            f.write(arch_analysis.to_markdown())
            f.write("\n\n")

            f.write("## Temporal Window Analysis\n\n")
            temp_analysis = df_summary.groupby('Temporal_Window').agg({
                'Mean_Distance_Error': ['mean', 'std', 'min', 'max'],
                'Accuracy': ['mean', 'std', 'min', 'max'],
                'Within_20cm': ['mean', 'std', 'min', 'max']
            }).round(4)
            f.write(temp_analysis.to_markdown())
            f.write("\n\n")

            f.write("## Key Insights for Collaborative Robotics\n\n")
            f.write("### 1. Spatial Accuracy vs Traditional Metrics\n")
            f.write("- Distance-based evaluation reveals significant differences in spatial precision\n")
            f.write("- Traditional accuracy doesn't always correlate with spatial performance\n")
            f.write("- Models optimized for classification may not be optimal for spatial applications\n\n")

            f.write("### 2. Architecture Performance Patterns\n")
            f.write("- **Complex GATv2**: Higher parameter count, stable convergence, good traditional metrics\n")
            f.write("- **Standard GATv2**: Efficient training, rapid convergence, balanced performance\n")
            f.write("- **ECC**: Variable performance, potential for spatial accuracy optimization\n")
            f.write("- **Enhanced GATv2**: Early stopping indicates potential overfitting issues\n\n")

            f.write("### 3. Training Efficiency vs Performance\n")
            f.write("- Longer training doesn't always guarantee better spatial accuracy\n")
            f.write("- Model size and parameter count show complex relationships with performance\n")
            f.write("- Convergence patterns affect final spatial precision\n\n")

            f.write("## Recommendations\n\n")
            f.write("### For High-Precision Applications (< 15cm tolerance):\n")
            best_15cm = df_summary.loc[df_summary['Within_15cm'].idxmax()]
            f.write(f"- **Recommended Model:** {best_15cm['Model']}\n")
            f.write(f"- **Performance:** {best_15cm['Within_15cm']:.1%} accuracy within 15cm\n")
            f.write(f"- **Trade-offs:** {best_15cm['Parameters']:,} parameters, {best_15cm['Training_Time']} training\n\n")

            f.write("### For Standard Applications (< 20cm tolerance):\n")
            best_20cm = df_summary.loc[df_summary['Within_20cm'].idxmax()]
            f.write(f"- **Recommended Model:** {best_20cm['Model']}\n")
            f.write(f"- **Performance:** {best_20cm['Within_20cm']:.1%} accuracy within 20cm\n")
            f.write(f"- **Trade-offs:** {best_20cm['Parameters']:,} parameters, {best_20cm['Training_Time']} training\n\n")

            f.write("### For Robust Applications (< 25cm tolerance):\n")
            best_25cm = df_summary.loc[df_summary['Within_25cm'].idxmax()]
            f.write(f"- **Recommended Model:** {best_25cm['Model']}\n")
            f.write(f"- **Performance:** {best_25cm['Within_25cm']:.1%} accuracy within 25cm\n")
            f.write(f"- **Trade-offs:** {best_25cm['Parameters']:,} parameters, {best_25cm['Training_Time']} training\n\n")

        print(f"📄 Generated report: {report_file}")

        return df_summary


def main():
    """Main execution function for targeted polygon-based distance evaluation."""
    print("🎯 Starting Targeted Polygon-Based Distance Evaluation System")
    print("📋 Evaluating 7 Specific Research Models")
    print("=" * 80)

    # Initialize evaluator
    evaluator = TargetedPolygonDistanceEvaluator()

    print(f"🔍 Target models discovered: {len(evaluator.target_models)}")
    for model in evaluator.target_models:
        print(f"   - {model.name}: {model.architecture}, {model.parameters:,} params")

    # Run targeted polygon distance evaluation
    results = evaluator.evaluate_all_target_models(max_samples=1000)

    if not results:
        print("❌ No successful evaluations!")
        return

    # Create visualizations
    evaluator.create_targeted_visualizations(results)

    # Generate summary report
    summary_df = evaluator.generate_targeted_report(results)

    print("\n" + "=" * 80)
    print("🎯 TARGETED POLYGON DISTANCE EVALUATION COMPLETED SUCCESSFULLY!")
    print("=" * 80)
    print(f"📊 Models evaluated: {len(results)}")
    print(f"📁 Results saved to: {evaluator.results_dir}")
    print("\n📈 Top 3 Models by Distance Accuracy:")

    # Show top performers
    top_models = summary_df.nsmallest(3, 'Mean_Distance_Error')
    for idx, row in top_models.iterrows():
        print(f"   {idx+1}. {row['Model']}: {row['Mean_Distance_Error']:.4f}m mean error")

    print("\n📊 Top 3 Models by 20cm Tolerance Accuracy:")
    top_tolerance = summary_df.nlargest(3, 'Within_20cm')
    for idx, row in top_tolerance.iterrows():
        print(f"   {idx+1}. {row['Model']}: {row['Within_20cm']:.4f} accuracy within 20cm")

    print("\n✅ Check the output directory for detailed results and visualizations!")
    print("📄 Key files generated:")
    print(f"   - targeted_polygon_distance_evaluation_results.csv")
    print(f"   - TARGETED_POLYGON_DISTANCE_EVALUATION_REPORT.md")
    print(f"   - targeted_polygon_distance_comprehensive_comparison.png")
    print(f"   - Individual model JSON files")


if __name__ == "__main__":
    main()
