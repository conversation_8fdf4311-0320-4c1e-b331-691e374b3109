import numpy as np
import matplotlib.pyplot as plt
import thesis_style
import os

thesis_style.set_thesis_style()

# Arena and grid
arena_w, arena_h = 21.06, 11.81
workstations = [(5.2,3.1), (8.7,6.4), (15.3,8.9), (18.1,4.2)]
grid_res = 0.1
grid_w, grid_h = int(arena_w/grid_res), int(arena_h/grid_res)

# Dummy accuracy data for illustration (replace with real arrays if available)
np.random.seed(42)
heatmaps = {
    'Standard_T3': np.random.uniform(0.7, 1.0, (grid_h, grid_w)),
    'Complex_T3': np.random.uniform(0.6, 0.95, (grid_h, grid_w)),
    'ECC_T3': np.random.uniform(0.5, 0.9, (grid_h, grid_w)),
}
stats = {
    'Standard_T3': 'Acc: 74.5%\nIoU: 62.0%',
    'Complex_T3': 'Acc: 73.5%\nIoU: 67.0%',
    'ECC_T3': 'Acc: 71.6%\nIoU: 56.7%',
}

fig, axs = plt.subplots(1, 3, figsize=(15, 6), sharey=True)
vmin, vmax = 0.5, 1.0
for i, (name, data) in enumerate(heatmaps.items()):
    im = axs[i].imshow(data, cmap='RdYlGn', vmin=vmin, vmax=vmax,
                      extent=[0, arena_w, 0, arena_h], origin='lower', aspect='auto')
    axs[i].set_title(name.replace('_', ' '), fontsize=14)
    axs[i].set_xlabel('X (m)')
    if i == 0:
        axs[i].set_ylabel('Y (m)')
    # Overlay workstations
    for wx, wy in workstations:
        rect = plt.Rectangle((wx-0.5, wy-0.5), 1, 1, linewidth=2, edgecolor='black', facecolor='none')
        axs[i].add_patch(rect)
    # Stats in corner
    axs[i].text(0.98, 0.02, stats[name], transform=axs[i].transAxes,
                ha='right', va='bottom', fontsize=12, color='black', bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))

# Shared colorbar
cbar = fig.colorbar(im, ax=axs, orientation='vertical', fraction=0.025, pad=0.04)
cbar.set_label('Accuracy', fontsize=12)

fig.suptitle('Fig 6.4: Spatial Accuracy Heatmaps', fontsize=14)
plt.tight_layout(rect=[0, 0, 1, 0.96])
os.makedirs('figures', exist_ok=True)
thesis_style.export_figure(fig, 'figures/Fig6_04_Spatial_Accuracy_Heatmaps.png')
plt.close(fig) 