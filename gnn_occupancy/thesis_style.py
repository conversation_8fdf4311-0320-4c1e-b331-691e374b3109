"""
Thesis Figure Style Guide

This file defines the global styling constants for all publication-quality figures
to ensure a consistent and professional appearance.
"""

# --- FONTS ---
FONT_FAMILY = "Arial"
TITLE_FONT_SIZE = 14
LABEL_FONT_SIZE = 12
LEGEND_FONT_SIZE = 10
TICK_FONT_SIZE = 10

# --- COLORS ---
COLORS = {
    "GATv2": "#2E86AB",
    "ECC": "#F18F01",
    "Success": "#4CAF50",
    "Failure": "#F44336",
    "Enhanced": "#A23E48",
    "Reference": "#B0B0B0",
    "Text": "#333333"
}

# --- HATCH PATTERNS ---
PATTERNS = {
    "T3": "",
    "T5": "///"
}

# --- FIGURE SETTINGS ---
DPI = 300
FIG_SIZE_STANDARD = (10, 6)
FIG_SIZE_WIDE = (12, 6)
FIG_SIZE_SQUARE = (8, 8)

# --- MATPLOTLIB RC PARAMS ---
# Apply these settings at the beginning of each plotting script
# import matplotlib.pyplot as plt
# plt.rcParams.update(STYLE_SETTINGS)
STYLE_SETTINGS = {
    "font.family": FONT_FAMILY,
    "axes.titlesize": TITLE_FONT_SIZE,
    "axes.labelsize": LABEL_FONT_SIZE,
    "xtick.labelsize": TICK_FONT_SIZE,
    "ytick.labelsize": TICK_FONT_SIZE,
    "legend.fontsize": LEGEND_FONT_SIZE,
    "figure.dpi": DPI,
    "savefig.dpi": DPI,
    "axes.edgecolor": COLORS["Text"],
    "axes.labelcolor": COLORS["Text"],
    "xtick.color": COLORS["Text"],
    "ytick.color": COLORS["Text"],
    "text.color": COLORS["Text"],
}