"""
Generate Figure 6.2: Architecture Family Radar Chart
"""


thesis_style.set_thesis_style()

# Radar chart data
labels = ['Accuracy', 'Reliability', 'Efficiency', 'Spatial_Precision', 'Training_Speed', 'Memory']
num_vars = len(labels)

# Data from prompt
families = ['GATv2', 'ECC', 'Enhanced']
data = [
    [0.85, 1.0, 0.90, 0.75, 0.70, 0.85],  # GATv2
    [0.65, 0.50, 0.20, 0.85, 0.95, 0.40],  # ECC
    [0.60, 0.0, 0.10, 0.70, 0.20, 0.30],   # Enhanced
]
colors = [thesis_style.COLORS['GATv2'], thesis_style.COLORS['ECC'], '#888888']

# Radar setup
angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()
angles += angles[:1]

fig, ax = plt.subplots(figsize=(7,7), subplot_kw=dict(polar=True))

for idx, (family, d, color) in enumerate(zip(families, data, colors)):
    values = d + d[:1]
    ax.plot(angles, values, color=color, linewidth=2, label=family, alpha=1)
    ax.fill(angles, values, color=color, alpha=0.4)

ax.set_theta_offset(np.pi / 2)
ax.set_theta_direction(-1)
ax.set_thetagrids(np.degrees(angles[:-1]), labels, fontsize=12)
ax.set_ylim(0, 1.1)
ax.set_title('Fig 6.2: Architecture Family Radar Chart', fontsize=14, y=1.08)
ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.1), fontsize=10)

plt.tight_layout()
os.makedirs('figures', exist_ok=True)
thesis_style.export_figure(fig, 'figures/Fig6_02_Architecture_Family_Radar.png')
plt.close(fig) 