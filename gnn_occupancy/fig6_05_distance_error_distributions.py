import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import thesis_style
import os

thesis_style.set_thesis_style()

# Dummy data for illustration (replace with real arrays)
np.random.seed(42)
models = ['Standard_T3', 'Complex_T3', 'ECC_T3']
dist_errors = [
    np.abs(np.random.normal(0.18, 0.05, 200)),
    np.abs(np.random.normal(0.20, 0.06, 200)),
    np.abs(np.random.normal(0.22, 0.07, 200)),
]
within_20cm = [3.95, 4.77, 5.41]  # from prompt

fig, (ax, ax_cdf) = plt.subplots(2, 1, figsize=(8,8), gridspec_kw={'height_ratios':[3,1]})

# Violin + box
sns.violinplot(data=dist_errors, ax=ax, inner=None, color='lightgray')
sns.boxplot(data=dist_errors, ax=ax, width=0.2, showcaps=True, boxprops={'facecolor':'none'}, showfliers=False, whiskerprops={'linewidth':2})
ax.set_xticklabels(models, fontsize=12)
ax.set_ylabel('Distance Error (m)', fontsize=12)
ax.set_title('Fig 6.5: Distance Error Distributions', fontsize=14)

# Tolerance lines
for tol in [0.15, 0.20, 0.25]:
    ax.axhline(tol, color='gray', linestyle='--', linewidth=1)
    ax.text(len(models)-0.5, tol+0.005, f'{tol*100:.0f}cm', color='gray', fontsize=10, va='bottom')

# Annotate means and within-tolerance
for i, (errs, pct) in enumerate(zip(dist_errors, within_20cm)):
    mean = np.mean(errs)
    ax.text(i, mean+0.01, f'Mean: {mean:.2f}m\nWithin 20cm: {pct:.2f}%',
            ha='center', va='bottom', fontsize=10, color='black', bbox=dict(facecolor='white', alpha=0.7, edgecolor='none'))

# CDF subplot
for i, errs in enumerate(dist_errors):
    sorted_errs = np.sort(errs)
    cdf = np.arange(1, len(errs)+1) / len(errs)
    ax_cdf.plot(sorted_errs, cdf, label=models[i])
ax_cdf.set_xlabel('Distance Error (m)', fontsize=12)
ax_cdf.set_ylabel('CDF', fontsize=10)
ax_cdf.legend(fontsize=10)
ax_cdf.grid(True, linestyle=':')

plt.tight_layout()
os.makedirs('figures', exist_ok=True)
thesis_style.export_figure(fig, 'figures/Fig6_05_Distance_Error_Distributions.png')
plt.close(fig) 