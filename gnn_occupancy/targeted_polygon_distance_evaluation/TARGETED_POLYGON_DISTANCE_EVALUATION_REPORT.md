# Targeted Polygon-Based Distance Evaluation Report

## Research Models Evaluation

**Generated:** 2025-06-06 10:35:41

**Models Evaluated:** 7 (7 target research models)

## Executive Summary

- **Best Traditional Accuracy:** Standard_GATv2_T3 (0.7448)
- **Best Distance Accuracy:** ECC_T5 (0.0000m)
- **Best 20cm Tolerance:** ECC_T3 (0.0541)

## Model Specifications

| Model | Architecture | Temporal Window | Parameters | Training Time | Model Size | Convergence |
|-------|-------------|----------------|------------|---------------|------------|-------------|
| Complex_GATv2_T3 | Complex GATv2 | T3 | 169,601 | 4.0h | 45MB | Stable |
| Complex_GATv2_T5 | Complex GATv2 | T5 | 169,601 | 2.5h | 45MB | Stable |
| Standard_GATv2_T3 | Standard GATv2 | T3 | 25,793 | 1.0h | 12MB | Rapid |
| Standard_GATv2_T5 | Standard GATv2 | T5 | 30,273 | 1.5h | 15MB | Rapid |
| ECC_T3 | ECC | T3 | 50,390,401 | 30min | 180MB | Fast |
| ECC_T5 | ECC | T5 | 2,106,977 | 21min | 55MB | Extended |
| Enhanced_GATv2_T3 | Enhanced GATv2 | T3 | 6,045,313 | 6.0h | 220MB | Early Stop |

## Performance Results

### Traditional Classification Metrics

| Model             |   Accuracy |   Precision |   Recall |   F1_Score |   ROC_AUC |
|:------------------|-----------:|------------:|---------:|-----------:|----------:|
| Complex_GATv2_T3  |   0.734876 |    0.755607 | 0.777055 |   0.766181 |         0 |
| Complex_GATv2_T5  |   0.687959 |    0.702755 | 0.767705 |   0.733796 |         0 |
| Standard_GATv2_T3 |   0.744793 |    0.713622 | 0.907747 |   0.799063 |         0 |
| Standard_GATv2_T5 |   0.688157 |    0.675843 | 0.851983 |   0.753759 |         0 |
| ECC_T3            |   0.716033 |    0.704523 | 0.847428 |   0.769396 |         0 |
| ECC_T5            |   0        |    0        | 0        |   0        |         0 |
| Enhanced_GATv2_T3 |   0        |    0        | 0        |   0        |         0 |

### Distance-Based Metrics

| Model             |   Mean_Distance_Error |   Median_Distance_Error |   P90_Distance |   P95_Distance |
|:------------------|----------------------:|------------------------:|---------------:|---------------:|
| Complex_GATv2_T3  |               3.05277 |                 2.82631 |        5.51483 |        5.75386 |
| Complex_GATv2_T5  |               2.98458 |                 2.82614 |        5.47794 |        5.69354 |
| Standard_GATv2_T3 |               2.99996 |                 2.81602 |        5.45112 |        5.66485 |
| Standard_GATv2_T5 |               3.01808 |                 2.83273 |        5.44994 |        5.66336 |
| ECC_T3            |               2.72115 |                 2.64465 |        5.44815 |        5.64258 |
| ECC_T5            |               0       |                 0       |        0       |        0       |
| Enhanced_GATv2_T3 |               0       |                 0       |        0       |        0       |

### Tolerance-Based Accuracy

| Model             |   Within_15cm |   Within_20cm |   Within_25cm |
|:------------------|--------------:|--------------:|--------------:|
| Complex_GATv2_T3  |     0.0310523 |     0.0477286 |     0.0707303 |
| Complex_GATv2_T5  |     0.0340357 |     0.0495948 |     0.0706645 |
| Standard_GATv2_T3 |     0.0283589 |     0.0395165 |     0.0478847 |
| Standard_GATv2_T5 |     0.0266854 |     0.0396067 |     0.0587079 |
| ECC_T3            |     0.0368732 |     0.0540806 |     0.0771878 |
| ECC_T5            |     0         |     0         |     0         |
| Enhanced_GATv2_T3 |     0         |     0         |     0         |

## Architecture Analysis

| Architecture   |   ('Mean_Distance_Error', 'mean') |   ('Mean_Distance_Error', 'std') |   ('Mean_Distance_Error', 'min') |   ('Mean_Distance_Error', 'max') |   ('Accuracy', 'mean') |   ('Accuracy', 'std') |   ('Accuracy', 'min') |   ('Accuracy', 'max') |   ('Within_20cm', 'mean') |   ('Within_20cm', 'std') |   ('Within_20cm', 'min') |   ('Within_20cm', 'max') |   ('Parameters', 'mean') |   ('Parameters', 'min') |   ('Parameters', 'max') |
|:---------------|----------------------------------:|---------------------------------:|---------------------------------:|---------------------------------:|-----------------------:|----------------------:|----------------------:|----------------------:|--------------------------:|-------------------------:|-------------------------:|-------------------------:|-------------------------:|------------------------:|------------------------:|
| Complex GATv2  |                            3.0187 |                           0.0482 |                           2.9846 |                           3.0528 |                 0.7114 |                0.0332 |                0.688  |                0.7349 |                    0.0487 |                   0.0013 |                   0.0477 |                   0.0496 |         169601           |        169601           |        169601           |
| ECC            |                            1.3606 |                           1.9241 |                           0      |                           2.7211 |                 0.358  |                0.5063 |                0      |                0.716  |                    0.027  |                   0.0382 |                   0      |                   0.0541 |              2.62487e+07 |             2.10698e+06 |             5.03904e+07 |
| Enhanced GATv2 |                            0      |                         nan      |                           0      |                           0      |                 0      |              nan      |                0      |                0      |                    0      |                 nan      |                   0      |                   0      |              6.04531e+06 |             6.04531e+06 |             6.04531e+06 |
| Standard GATv2 |                            3.009  |                           0.0128 |                           3      |                           3.0181 |                 0.7165 |                0.04   |                0.6882 |                0.7448 |                    0.0396 |                   0.0001 |                   0.0395 |                   0.0396 |          28033           |         25793           |         30273           |

## Temporal Window Analysis

| Temporal_Window   |   ('Mean_Distance_Error', 'mean') |   ('Mean_Distance_Error', 'std') |   ('Mean_Distance_Error', 'min') |   ('Mean_Distance_Error', 'max') |   ('Accuracy', 'mean') |   ('Accuracy', 'std') |   ('Accuracy', 'min') |   ('Accuracy', 'max') |   ('Within_20cm', 'mean') |   ('Within_20cm', 'std') |   ('Within_20cm', 'min') |   ('Within_20cm', 'max') |
|:------------------|----------------------------------:|---------------------------------:|---------------------------------:|---------------------------------:|-----------------------:|----------------------:|----------------------:|----------------------:|--------------------------:|-------------------------:|-------------------------:|-------------------------:|
| T3                |                            2.1935 |                           1.4695 |                                0 |                           3.0528 |                 0.5489 |                0.3661 |                     0 |                0.7448 |                    0.0353 |                   0.0243 |                        0 |                   0.0541 |
| T5                |                            2.0009 |                           1.7329 |                                0 |                           3.0181 |                 0.4587 |                0.3973 |                     0 |                0.6882 |                    0.0297 |                   0.0262 |                        0 |                   0.0496 |

## Key Insights for Collaborative Robotics

### 1. Spatial Accuracy vs Traditional Metrics
- Distance-based evaluation reveals significant differences in spatial precision
- Traditional accuracy doesn't always correlate with spatial performance
- Models optimized for classification may not be optimal for spatial applications

### 2. Architecture Performance Patterns
- **Complex GATv2**: Higher parameter count, stable convergence, good traditional metrics
- **Standard GATv2**: Efficient training, rapid convergence, balanced performance
- **ECC**: Variable performance, potential for spatial accuracy optimization
- **Enhanced GATv2**: Early stopping indicates potential overfitting issues

### 3. Training Efficiency vs Performance
- Longer training doesn't always guarantee better spatial accuracy
- Model size and parameter count show complex relationships with performance
- Convergence patterns affect final spatial precision

## Recommendations

### For High-Precision Applications (< 15cm tolerance):
- **Recommended Model:** ECC_T3
- **Performance:** 3.7% accuracy within 15cm
- **Trade-offs:** 50,390,401 parameters, 30min training

### For Standard Applications (< 20cm tolerance):
- **Recommended Model:** ECC_T3
- **Performance:** 5.4% accuracy within 20cm
- **Trade-offs:** 50,390,401 parameters, 30min training

### For Robust Applications (< 25cm tolerance):
- **Recommended Model:** ECC_T3
- **Performance:** 7.7% accuracy within 25cm
- **Trade-offs:** 50,390,401 parameters, 30min training

