"""
Generate Figure 6.1: Performance Hierarchy Bar Chart
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# Import project-specific modules
import thesis_style
from load_project_data import get_performance_data

def create_performance_hierarchy_chart(df):
    """
    Creates and saves the grouped bar chart for model performance.
    """
    # Apply the global style settings
    plt.rcParams.update(thesis_style.STYLE_SETTINGS)

    # Prepare data
    df_bars = df[df['Status'] == 'OK'].copy()
    # Sort by a composite performance score for ranking
    df_bars['PerfSum'] = df_bars[['Accuracy', 'F1', 'IoU']].sum(axis=1)
    df_bars = df_bars.sort_values('PerfSum', ascending=False)

    metrics = ['Accuracy', 'F1', 'IoU']
    model_labels = df_bars['Model'].str.replace('_', ' ').str.replace(' GATv2', '')
    x = np.arange(len(model_labels))
    bar_width = 0.22
    
    fig, ax = plt.subplots(figsize=thesis_style.FIG_SIZE_WIDE)

    # Create bars for each metric
    for i, metric in enumerate(metrics):
        offset = bar_width * (i - 1)
        bars = ax.bar(x + offset, df_bars[metric], bar_width, label=metric)
        # Add value labels on top of bars
        for bar in bars:
            yval = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2.0, yval + 1, f'{yval:.1f}', ha='center', va='bottom', fontsize=8)

    # Color bars by architecture and hatch by temporal config
    for i, model_name in enumerate(df_bars['Model']):
        arch = df_bars.iloc[i]['Architecture']
        config = df_bars.iloc[i]['Config']
        color = thesis_style.COLORS.get(arch, 'gray')
        hatch = thesis_style.PATTERNS.get(config, '')
        
        # Apply to the three bars for this model
        for j in range(len(metrics)):
            offset = bar_width * (j - 1)
            ax.patches[i + j * len(df_bars)].set_facecolor(color)
            ax.patches[i + j * len(df_bars)].set_hatch(hatch)
            ax.patches[i + j * len(df_bars)].set_edgecolor('black')


    # Add reference lines
    for y in [60, 70, 80]:
        ax.axhline(y=y, color=thesis_style.COLORS['Reference'], linestyle='--', linewidth=0.8, zorder=0)
        ax.text(len(model_labels) - 0.5, y, f'{y}%', color=thesis_style.COLORS['Reference'], va='center', ha='right', fontsize=9, backgroundcolor='white')

    # Annotate the failed model
    failed_model_name = 'ECC T5'
    ax.text(len(model_labels) - 0.5, 10, f'{failed_model_name}\nFAILED',
            ha='center', va='center', color=thesis_style.COLORS['Failure'],
            fontweight='bold', fontsize=10,
            bbox=dict(facecolor='white', alpha=0.8, edgecolor='none', pad=0.2))

    # --- Final Touches ---
    ax.set_ylabel('Performance Score (%)')
    ax.set_title('Figure 6.1: GNN Model Performance Hierarchy')
    ax.set_xticks(x)
    ax.set_xticklabels(model_labels, rotation=45, ha='right')
    ax.set_ylim(0, 100)
    ax.legend(title='Metrics', loc='upper right')
    
    # Create custom legend for architecture and config
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor=thesis_style.COLORS['GATv2'], label='GATv2 Architecture'),
        Patch(facecolor=thesis_style.COLORS['ECC'], label='ECC Architecture'),
        Patch(facecolor='white', hatch=thesis_style.PATTERNS['T3'], edgecolor='black', label='T3 (3-frame window)'),
        Patch(facecolor='white', hatch=thesis_style.PATTERNS['T5'], edgecolor='black', label='T5 (5-frame window)')
    ]
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0, 0.9))


    fig.tight_layout()

    # --- Save Figure ---
    if not os.path.exists('figures'):
        os.makedirs('figures')
    save_path = 'figures/Fig6_01_Performance_Hierarchy.png'
    plt.savefig(save_path, dpi=thesis_style.DPI)
    print(f"Figure saved to {save_path}")

if __name__ == '__main__':
    performance_df = get_performance_data()
    create_performance_hierarchy_chart(performance_df)