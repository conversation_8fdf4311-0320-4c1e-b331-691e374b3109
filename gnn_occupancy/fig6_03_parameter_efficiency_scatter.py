import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import thesis_style
import os

thesis_style.set_thesis_style()

# Data (from prompt)
data = [
    ['Standard_GATv2_T3', 'GATv2', 'T3', 74.48, 79.91, 62.00, 26000, 'OK'],
    ['Complex_GATv2_T3', 'GATv2', 'T3', 73.49, 76.62, 66.99, 170000, 'OK'],
    ['Standard_GATv2_T5', 'GATv2', 'T5', 68.82, 75.38, 58.62, 30000, 'OK'],
    ['Complex_GATv2_T5', 'GATv2', 'T5', 68.80, 73.38, 58.62, 170000, 'OK'],
    ['Enhanced_GATv2_T3', 'GATv2', 'T3', 67.25, 72.15, 62.00, 6000000, 'OK'],
    ['ECC_T3', 'ECC', 'T3', 71.60, 76.94, 56.69, 50400000, 'OK'],
]
df = pd.DataFrame(data, columns=['Model','Architecture','Config','Accuracy','F1','IoU','Params','Status'])
df['log_Params'] = np.log10(df['Params'])

fig, ax = plt.subplots(figsize=(8,6))

for arch in df['Architecture'].unique():
    sub = df[df['Architecture']==arch]
    ax.scatter(sub['log_Params'], sub['F1'],
               s=sub['IoU']*4,  # scale IoU for bubble size
               c=thesis_style.COLORS[arch], label=arch, alpha=0.7, edgecolor='black')

# Efficiency frontier (convex hull of best F1 at lowest params)
frontier = df.sort_values(['log_Params','F1'], ascending=[True,False]).drop_duplicates('log_Params')
ax.plot(frontier['log_Params'].values, frontier['F1'].values, '--', color='black', lw=1, label='Efficiency Frontier')

# Annotate champion
champ = df.loc[df['F1'].idxmax()]
ax.annotate('Efficiency Champion',
            xy=(champ['log_Params'], champ['F1']),
            xytext=(champ['log_Params']-0.2, champ['F1']+2),
            arrowprops=dict(facecolor='green', shrink=0.05),
            fontsize=12, color='green', fontweight='bold')

# Diagonal efficiency lines
for slope in [10, 20, 40]:
    x_vals = np.array([4, 8])
    ax.plot(x_vals, slope*(x_vals-x_vals[0])+60, ':', color='gray', alpha=0.3)
    ax.text(x_vals[1], slope*(x_vals[1]-x_vals[0])+60, f'{slope}pp/dec', color='gray', fontsize=10)

ax.set_xlabel('log10(Parameters)')
ax.set_ylabel('F1-Score')
ax.set_title('Fig 6.3: Parameter Efficiency Scatter', fontsize=14)
ax.legend(fontsize=10)
plt.tight_layout()
os.makedirs('figures', exist_ok=True)
thesis_style.export_figure(fig, 'figures/Fig6_03_Parameter_Efficiency_Scatter.png')
plt.close(fig) 