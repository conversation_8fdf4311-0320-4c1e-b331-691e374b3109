import matplotlib.pyplot as plt
import numpy as np
import thesis_style
import os

thesis_style.set_thesis_style()

fig, axs = plt.subplots(1, 4, figsize=(16, 4))

# Panel A: Predictions vs ground truth polygons
poly_gt = np.array([[1,1],[2,1],[2,2],[1,2]])
poly_pred = np.array([[1.1,1.1],[2.1,1.0],[2.0,2.1],[1.0,2.0]])
axs[0].fill(*poly_gt.T, color='none', edgecolor='black', lw=2, label='Ground Truth')
axs[0].fill(*poly_pred.T, color='#2E86AB', alpha=0.3, label='Prediction')
axs[0].set_title('A: Prediction vs GT')
axs[0].legend(fontsize=10)

# Panel B: Distance calculation with arrows
axs[1].fill(*poly_gt.T, color='none', edgecolor='black', lw=2)
axs[1].fill(*poly_pred.T, color='#2E86AB', alpha=0.3)
for i in range(len(poly_pred)):
    axs[1].arrow(poly_pred[i,0], poly_pred[i,1], poly_gt[i,0]-poly_pred[i,0], poly_gt[i,1]-poly_pred[i,1],
                 head_width=0.05, head_length=0.08, fc='red', ec='red', length_includes_head=True)
axs[1].set_title('B: Distance Calculation')

# Panel C: Tolerance zones as colored bands
axs[2].fill(*poly_gt.T, color='none', edgecolor='black', lw=2)
axs[2].fill(*poly_pred.T, color='#2E86AB', alpha=0.3)
for tol, color in zip([0.15, 0.20, 0.25], ['#4CAF50', '#F18F01', '#F44336']):
    circle = plt.Circle((1.5,1.5), tol, color=color, alpha=0.2)
    axs[2].add_patch(circle)
axs[2].set_title('C: Tolerance Zones')

# Panel D: Accuracy classification
axs[3].fill(*poly_gt.T, color='none', edgecolor='black', lw=2)
axs[3].fill(*poly_pred.T, color='#2E86AB', alpha=0.3)
axs[3].scatter([1.1,2.1], [1.1,1.0], color='#4CAF50', label='Correct', s=60)
axs[3].scatter([2.0,1.0], [2.1,2.0], color='#F44336', label='Incorrect', s=60)
axs[3].set_title('D: Accuracy Classification')
axs[3].legend(fontsize=10)

for ax in axs:
    ax.set_xlim(0.8,2.3)
    ax.set_ylim(0.8,2.3)
    ax.set_aspect('equal')
    ax.axis('off')

fig.suptitle('Fig 6.6: Polygon Evaluation Method', fontsize=14)
plt.tight_layout(rect=[0, 0, 1, 0.95])
os.makedirs('figures', exist_ok=True)
thesis_style.export_figure(fig, 'figures/Fig6_06_Polygon_Evaluation_Method.png')
plt.close(fig) 