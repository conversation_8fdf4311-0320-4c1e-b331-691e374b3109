# GNN-based Occupancy Prediction System for Collaborative Robotics

This repository contains a PyTorch-based implementation of a Graph Neural Network (GNN) system for binary occupancy prediction in collaborative robotics environments. The system uses preprocessed point cloud data with temporal information to predict whether points in a robotic environment are occupied or unoccupied.

## System Overview

The system consists of the following components:

1. **Data Processing Module**: Loads and preprocesses point cloud data from .pt files, converts 5-class labels to binary labels, and applies data augmentation.
2. **GNN Architecture**: Implements a flexible GNN model with different layer types (GraphSAGE, GATv2, ECC), skip connections, batch normalization, and global pooling.
3. **Training Pipeline**: Handles optimization, loss calculation, learning rate scheduling, early stopping, and metrics tracking.
4. **Evaluation Module**: Calculates metrics, generates confusion matrices, plots ROC curves, and visualizes point clouds with predictions.
5. **Ablation Studies**: Compares different GNN layer types and temporal window sizes.

## Project Structure

```
gnn_occupancy/
├── config.yaml            # Configuration file
├── main.py                # Main script
├── data.py                # Dataset and data loader implementations
├── model.py               # GNN architecture
├── train.py               # Training pipeline
├── evaluate.py            # Evaluation tools
├── ablation.py            # Ablation studies
├── utils.py               # Utility functions
├── checkpoints/           # Model checkpoints
└── visualizations/        # Visualizations
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/thiyanayugi/gnn-occupancy-prediction.git
cd gnn-occupancy-prediction
```

2. Install dependencies:
```bash
pip install torch torch-geometric numpy matplotlib scikit-learn tqdm pyyaml seaborn pandas
```

## Usage

### Configuration

The system is configured using a YAML file (`config.yaml`). You can modify this file to change the model architecture, training parameters, and evaluation settings.

### Training

To train the model:

```bash
python main.py --mode train
```

You can specify a specific temporal window size:

```bash
python main.py --mode train --temporal_window 3
```

Or a specific GNN type:

```bash
python main.py --mode train --gnn_type gatv2
```

### Evaluation

To evaluate the model:

```bash
python main.py --mode evaluate
```

### Ablation Studies

To run ablation studies:

```bash
python main.py --mode ablation
```

### All-in-One

To train, evaluate, and run ablation studies:

```bash
python main.py --mode all
```

## Data Format

The system expects data in PyTorch Geometric's `Data` format, with the following attributes:

- `x`: Node features (13 dimensions for temporal_1, 14 dimensions for temporal_3 and temporal_5)
- `edge_index`: Edge indices
- `y`: Node labels (0: Unknown, 1: Workstation, 2: Robot, 3: Boundary, 4: KLT)
- `pos`: Node positions in 3D space

The system converts the 5-class labels to binary labels:
- Occupied (1): Workstation (1), Robot (2), Boundary (3), KLT (4)
- Unoccupied (0): Unknown (0)

## GNN Architecture

The system implements three types of GNN layers:

1. **GraphSAGE**: A scalable approach for inductive representation learning on large graphs.
2. **GATv2**: An improved version of Graph Attention Networks with more expressive attention mechanisms.
3. **Edge-Conditioned Convolution (ECC)**: A convolution operation that applies different weights to different edges based on edge features.

The model architecture includes:
- Initial feature embedding layer
- Multiple GNN layers with skip connections
- Batch normalization
- Global pooling (mean, max, or combined)
- MLP classifier for binary prediction

## Results

The system generates various visualizations and metrics:

- Training and validation curves (loss, accuracy, F1 score, ROC AUC)
- Confusion matrices
- ROC curves
- Point cloud visualizations with predictions
- Feature importance analysis
- Ablation study results

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

Thiyanayugi Mariraj (2025)

# PhD Thesis Figures: GNN Occupancy Evaluation

This repository contains scripts to generate all essential figures for the PhD thesis evaluation of GNN-based occupancy models.

## Directory Structure
- `figures/` — Output folder for all publication-ready PNGs
- `data/` — Input CSVs and arrays (performance, spatial, training data)
- `models_final/` — Model outputs and evaluation results
- `thesis_style.py` — Global style and color settings
- `fig6_01_performance_hierarchy.py`, ... — Scripts for each figure

## Requirements
- Python 3.8+
- matplotlib
- seaborn
- pandas
- numpy
- scipy

Install dependencies:
```bash
pip install matplotlib seaborn pandas numpy scipy
```

## Usage
To generate all figures:
```bash
python fig6_01_performance_hierarchy.py
# ...repeat for each script (fig6_02_..., etc.)
```
All figures will be saved in the `figures/` directory at 300 DPI, ready for publication.

## Notes
- Update the `data/` and `models_final/` folders with your latest results for full scientific accuracy.
- All scripts use consistent fonts, colors, and layouts as per thesis standards.
- For questions or issues, contact the repository maintainer.
