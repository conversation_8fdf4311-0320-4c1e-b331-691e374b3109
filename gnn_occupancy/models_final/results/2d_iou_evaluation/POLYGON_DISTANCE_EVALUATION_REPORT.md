# Polygon-Based Distance Evaluation Report

**Generated:** 2025-06-06 09:48:10

**Models Evaluated:** 5

## Executive Summary

- **Best Traditional Accuracy:** ecc_3L_64H_temp5_checkpoints_ecc_temp5 (0.0000)
- **Best Distance Accuracy:** ecc_3L_64H_temp5_checkpoints_ecc_temp5 (0.0000m)
- **Best 20cm Tolerance:** ecc_3L_64H_temp5_checkpoints_ecc_temp5 (0.0000)

## Key Findings

### Spatial Accuracy Insights

1. **Distance-based evaluation provides more nuanced insights** into model performance for spatial applications compared to traditional IoU metrics.

2. **Tolerance-based accuracy** shows how models perform at different precision requirements for collaborative robotics.

3. **Architecture differences** are more pronounced when evaluated using spatial distance metrics.

## Detailed Results

### Traditional Classification Metrics

| Model                                                       |   Accuracy |   Precision |   Recall |   F1_Score |   ROC_AUC |
|:------------------------------------------------------------|-----------:|------------:|---------:|-----------:|----------:|
| ecc_3L_64H_temp5_checkpoints_ecc_temp5                      |          0 |           0 |        0 |          0 |         0 |
| gatv2_3L_64H_temp3_checkpoints_gatv2_temp3                  |          0 |           0 |        0 |          0 |         0 |
| ecc_3L_64H_temp3_checkpoints_ecc_temp3                      |          0 |           0 |        0 |          0 |         0 |
| gatv2_4L_128H_temp3_checkpoints_gatv2_complex_4layers_temp3 |          0 |           0 |        0 |          0 |         0 |
| gatv2_3L_64H_temp5_checkpoints_temp5                        |          0 |           0 |        0 |          0 |         0 |

### Distance-Based Metrics

| Model                                                       |   Mean_Distance_Error |   Median_Distance_Error |   P90_Distance |   P95_Distance |
|:------------------------------------------------------------|----------------------:|------------------------:|---------------:|---------------:|
| ecc_3L_64H_temp5_checkpoints_ecc_temp5                      |                     0 |                       0 |              0 |              0 |
| gatv2_3L_64H_temp3_checkpoints_gatv2_temp3                  |                     0 |                       0 |              0 |              0 |
| ecc_3L_64H_temp3_checkpoints_ecc_temp3                      |                     0 |                       0 |              0 |              0 |
| gatv2_4L_128H_temp3_checkpoints_gatv2_complex_4layers_temp3 |                     0 |                       0 |              0 |              0 |
| gatv2_3L_64H_temp5_checkpoints_temp5                        |                     0 |                       0 |              0 |              0 |

### Tolerance-Based Accuracy

| Model                                                       |   Within_15cm |   Within_20cm |   Within_25cm |
|:------------------------------------------------------------|--------------:|--------------:|--------------:|
| ecc_3L_64H_temp5_checkpoints_ecc_temp5                      |             0 |             0 |             0 |
| gatv2_3L_64H_temp3_checkpoints_gatv2_temp3                  |             0 |             0 |             0 |
| ecc_3L_64H_temp3_checkpoints_ecc_temp3                      |             0 |             0 |             0 |
| gatv2_4L_128H_temp3_checkpoints_gatv2_complex_4layers_temp3 |             0 |             0 |             0 |
| gatv2_3L_64H_temp5_checkpoints_temp5                        |             0 |             0 |             0 |

## Architecture Analysis

| Architecture   |   ('Mean_Distance_Error', 'mean') |   ('Mean_Distance_Error', 'std') |   ('Accuracy', 'mean') |   ('Accuracy', 'std') |   ('Within_20cm', 'mean') |   ('Within_20cm', 'std') |
|:---------------|----------------------------------:|---------------------------------:|-----------------------:|----------------------:|--------------------------:|-------------------------:|
| ecc            |                                 0 |                                0 |                      0 |                     0 |                         0 |                        0 |
| gatv2          |                                 0 |                                0 |                      0 |                     0 |                         0 |                        0 |

## Temporal Window Analysis

| Temporal_Window   |   ('Mean_Distance_Error', 'mean') |   ('Mean_Distance_Error', 'std') |   ('Accuracy', 'mean') |   ('Accuracy', 'std') |   ('Within_20cm', 'mean') |   ('Within_20cm', 'std') |
|:------------------|----------------------------------:|---------------------------------:|-----------------------:|----------------------:|--------------------------:|-------------------------:|
| T3                |                                 0 |                                0 |                      0 |                     0 |                         0 |                        0 |
| T5                |                                 0 |                                0 |                      0 |                     0 |                         0 |                        0 |

## Recommendations for Collaborative Robotics

1. **For High-Precision Applications (< 15cm tolerance):** Use models with lowest mean distance error

2. **For Standard Applications (< 20cm tolerance):** Balance between distance accuracy and computational efficiency

3. **For Robust Applications (< 25cm tolerance):** Consider models with highest tolerance-based accuracy

